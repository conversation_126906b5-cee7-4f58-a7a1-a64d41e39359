基于区块链的专利交易系统

大致描述
包含三种角色，普通用户、专利审核方以及管理员。
普通用户可以上传专利、搜索专利、专利维权、专利交易、查看个人信息、查看购买和发布的专利
专利审核方可以审核专利（包括上传、维权和交易）
管理员可以查看所有用户以及交易记录

前端通过vue+bootstrap开发，使用metamask登录。后端使用javascript+ipfs+ganache实现，文档存入ipfs，其余数据存储在ganache上。

1. 专利上传：专利名称，专利号，专利类别，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人姓名，专利权人身份证号。是否为代理出售，是就上传专利代理委托证明文档，不是就上传专利权证明文档

2. 专利搜索：根据专利名、专利号以及专利类别进行模糊搜索，对于搜索后的专利可以查看详细信息并且可以下载文件。应当保留每一个专利操作的流转过程，包括每一个参与方的操作行为和时间

3. 专利交易：可以购买交易，购买后需要通过专利审核方进行审核，通过后完成交易，用户可查看交易状态

4. 专利信息查看：查看自己购买的专利、卖出的专利以及上传的专利

5. 专利维权：可以对专利进行维权，维权成功后该专利从被维权方转移至维权方，需要审核方审核

6. 专利审核：由专利审核方审核，包括上传审核（通过与否）、维权审核（通过与否）、交易审核（通过与否）

