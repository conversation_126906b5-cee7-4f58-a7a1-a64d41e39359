# 前端开发待办清单

## 基础设置
- [ ] Vue + Bootstrap 项目初始化
- [ ] MetaMask 钱包连接与登录功能
- [ ] 路由配置（用户/审核方/管理员）

## 用户界面
- [ ] 专利上传页面（表单+文件上传）
- [ ] 专利搜索页面（搜索框+结果列表）
- [ ] 专利详情页面（查看+下载）
- [ ] 专利交易页面（购买功能）
- [ ] 专利维权页面（维权申请）
- [ ] 个人中心页面（购买/发布/上传的专利）

## 审核方界面
- [ ] 审核列表页面
- [ ] 上传审核页面
- [ ] 交易审核页面
- [ ] 维权审核页面

## 管理员界面
- [ ] 用户管理页面
- [ ] 交易记录查看页面

## 通用组件
- [ ] 页面布局组件
- [ ] 专利卡片组件
- [ ] 状态显示组件
- [ ] 文件上传组件

## 功能特性
- [ ] 专利操作流转记录显示
- [ ] 交易状态跟踪
- [ ] 响应式设计 